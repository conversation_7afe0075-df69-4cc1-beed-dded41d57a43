<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" type="image/png" sizes="32x32" href="./images/favicon-32x32.png">
  <title>Frontend Mentor | Fylo landing page with dark theme and features grid</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;700&family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">

  <!-- Font Awesome for social icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Open Sans', sans-serif;
      background-color: hsl(218, 28%, 13%);
      color: hsl(0, 0%, 100%);
      line-height: 1.6;
    }

    .container {
      max-width: 1440px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Header */
    .header {
      background-color: hsl(217, 28%, 15%);
      padding: 50px 0;
    }

    .nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      height: 50px;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 50px;
    }

    .nav-links a {
      color: hsl(0, 0%, 100%);
      text-decoration: none;
      font-family: 'Raleway', sans-serif;
      font-size: 16px;
      transition: color 0.3s ease;
    }

    .nav-links a:hover {
      color: hsl(176, 68%, 64%);
    }

    /* Hero Section */
    .hero {
      background-color: hsl(217, 28%, 15%);
      background-image: url('./images/bg-curvy-desktop.svg');
      background-repeat: no-repeat;
      background-position: bottom;
      background-size: contain;
      text-align: center;
      padding: 80px 0 200px;
    }

    .hero-illustration {
      max-width: 720px;
      width: 100%;
      margin-bottom: 40px;
    }

    .hero h1 {
      font-family: 'Raleway', sans-serif;
      font-size: 40px;
      font-weight: 700;
      line-height: 1.5;
      margin-bottom: 30px;
      max-width: 720px;
      margin-left: auto;
      margin-right: auto;
    }

    .hero p {
      font-size: 20px;
      line-height: 1.5;
      margin-bottom: 40px;
      max-width: 590px;
      margin-left: auto;
      margin-right: auto;
    }

    .btn {
      background: linear-gradient(135deg, hsl(176, 68%, 64%), hsl(198, 60%, 50%));
      color: hsl(0, 0%, 100%);
      padding: 18px 80px;
      border: none;
      border-radius: 30px;
      font-family: 'Raleway', sans-serif;
      font-size: 16px;
      font-weight: 700;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
    }

    .btn:hover {
      background: linear-gradient(135deg, hsl(176, 68%, 74%), hsl(198, 60%, 60%));
      transform: translateY(-2px);
    }

    /* Features Section */
    .features {
      padding: 100px 0;
      background-color: hsl(218, 28%, 13%);
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 100px 150px;
      max-width: 880px;
      margin: 0 auto;
    }

    .feature {
      text-align: center;
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      margin-bottom: 30px;
    }

    .feature h3 {
      font-family: 'Raleway', sans-serif;
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .feature p {
      font-size: 14px;
      line-height: 1.6;
    }

    /* Stay Productive Section */
    .stay-productive {
      padding: 100px 0;
      background-color: hsl(218, 28%, 13%);
    }

    .productive-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 80px;
      align-items: center;
    }

    .productive-illustration {
      max-width: 100%;
    }

    .productive-text h2 {
      font-family: 'Raleway', sans-serif;
      font-size: 40px;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 20px;
      max-width: 400px;
    }

    .productive-text p {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .see-how-link {
      color: hsl(176, 68%, 64%);
      text-decoration: none;
      border-bottom: 1px solid hsl(176, 68%, 64%);
      padding-bottom: 5px;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: color 0.3s ease;
    }

    .see-how-link:hover {
      color: hsl(0, 0%, 100%);
      border-bottom-color: hsl(0, 0%, 100%);
    }

    .arrow-icon {
      width: 16px;
      height: 16px;
    }

    /* Testimonials Section */
    .testimonials {
      padding: 100px 0;
      background-color: hsl(218, 28%, 13%);
      position: relative;
    }

    .testimonials::before {
      content: '';
      position: absolute;
      top: 80px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 45px;
      background-image: url('./images/bg-quotes.png');
      background-size: contain;
      background-repeat: no-repeat;
      z-index: 1;
    }

    .testimonials-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;
      position: relative;
      z-index: 2;
    }

    .testimonial {
      background-color: hsl(219, 30%, 18%);
      padding: 40px 25px 25px;
      border-radius: 5px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .testimonial:first-child {
      padding-top: 50px;
    }

    .testimonial p {
      font-size: 14px;
      line-height: 1.8;
      margin-bottom: 25px;
    }

    .testimonial-author {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .author-avatar {
      width: 30px;
      height: 30px;
      border-radius: 50%;
    }

    .author-info h4 {
      font-size: 12px;
      font-weight: 700;
      margin-bottom: 2px;
    }

    .author-info p {
      font-size: 10px;
      margin: 0;
    }

    /* CTA Section */
    .cta {
      padding: 0 0 100px;
      background-color: hsl(218, 28%, 13%);
      position: relative;
    }

    .cta-box {
      background-color: hsl(217, 28%, 15%);
      padding: 45px 80px;
      border-radius: 10px;
      text-align: center;
      max-width: 860px;
      margin: 0 auto;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 2;
    }

    .cta h2 {
      font-family: 'Raleway', sans-serif;
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 20px;
    }

    .cta p {
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 40px;
      max-width: 620px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-form {
      display: flex;
      gap: 30px;
      align-items: flex-start;
    }

    .cta-input {
      flex: 1;
      padding: 18px 40px;
      border: none;
      border-radius: 30px;
      font-size: 14px;
      outline: none;
    }

    .cta-input::placeholder {
      color: hsl(0, 0%, 75%);
    }

    .cta-btn {
      background: linear-gradient(135deg, hsl(176, 68%, 64%), hsl(198, 60%, 50%));
      color: hsl(0, 0%, 100%);
      padding: 18px 30px;
      border: none;
      border-radius: 30px;
      font-family: 'Raleway', sans-serif;
      font-size: 14px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .cta-btn:hover {
      background: linear-gradient(135deg, hsl(176, 68%, 74%), hsl(198, 60%, 60%));
    }

    /* Footer */
    .footer {
      background-color: hsl(216, 53%, 9%);
      padding: 200px 0 50px;
      margin-top: -150px;
      position: relative;
    }

    .footer-content {
      display: grid;
      grid-template-columns: 1fr 150px 150px 200px;
      gap: 60px;
      align-items: start;
      margin-bottom: 40px;
    }

    .footer-logo {
      height: 40px;
      margin-bottom: 40px;
    }

    .footer-contact {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .contact-item {
      display: flex;
      align-items: flex-start;
      gap: 20px;
      font-size: 14px;
      line-height: 1.6;
    }

    .contact-icon {
      width: 18px;
      height: 18px;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .footer-links {
      list-style: none;
    }

    .footer-links li {
      margin-bottom: 15px;
    }

    .footer-links a {
      color: hsl(0, 0%, 100%);
      text-decoration: none;
      font-size: 14px;
      transition: color 0.3s ease;
    }

    .footer-links a:hover {
      color: hsl(176, 68%, 64%);
    }

    .social-links {
      display: flex;
      gap: 12px;
      justify-content: flex-start;
    }

    .social-link {
      width: 32px;
      height: 32px;
      border: 1px solid hsl(0, 0%, 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: hsl(0, 0%, 100%);
      text-decoration: none;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .social-link:hover {
      color: hsl(176, 68%, 64%);
      border-color: hsl(176, 68%, 64%);
    }

    .attribution {
      font-size: 11px;
      text-align: center;
      margin-top: 50px;
    }

    .attribution a {
      color: hsl(228, 45%, 44%);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
      .container {
        padding: 0 15px;
      }

      .nav-links {
        gap: 25px;
      }

      .nav-links a {
        font-size: 14px;
      }

      .hero {
        background-image: url('./images/bg-curvy-mobile.svg');
        padding: 50px 0 150px;
      }

      .hero h1 {
        font-size: 24px;
      }

      .hero p {
        font-size: 14px;
      }

      .features-grid {
        grid-template-columns: 1fr;
        gap: 80px;
        max-width: 320px;
      }

      .productive-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
      }

      .productive-text h2 {
        font-size: 18px;
        max-width: none;
      }

      .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 25px;
      }

      .cta-box {
        padding: 40px 30px;
      }

      .cta h2 {
        font-size: 18px;
      }

      .cta-form {
        flex-direction: column;
        gap: 25px;
      }

      .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
      }

      .footer-social {
        text-align: center;
      }

      .social-links {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="header">
    <div class="container">
      <nav class="nav">
        <img src="./images/logo.svg" alt="Fylo" class="logo">
        <ul class="nav-links">
          <li><a href="#features">Features</a></li>
          <li><a href="#team">Team</a></li>
          <li><a href="#signin">Sign In</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <img src="./images/illustration-intro.png" alt="Fylo Illustration" class="hero-illustration">
      <h1>All your files in one secure location, accessible anywhere.</h1>
      <p>Fylo stores all your most important files in one secure location. Access them wherever you need, share and collaborate with friends family, and co-workers.</p>
      <a href="#" class="btn">Get Started</a>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features" id="features">
    <div class="container">
      <div class="features-grid">
        <div class="feature">
          <img src="./images/icon-access-anywhere.svg" alt="Access Anywhere" class="feature-icon">
          <h3>Access your files, anywhere</h3>
          <p>The ability to use a smartphone, tablet, or computer to access your account means your files follow you everywhere.</p>
        </div>
        <div class="feature">
          <img src="./images/icon-security.svg" alt="Security" class="feature-icon">
          <h3>Security you can trust</h3>
          <p>2-factor authentication and user-controlled encryption are just a couple of the security features we allow to help secure your files.</p>
        </div>
        <div class="feature">
          <img src="./images/icon-collaboration.svg" alt="Collaboration" class="feature-icon">
          <h3>Real-time collaboration</h3>
          <p>Securely share files and folders with friends, family and colleagues for live collaboration. No email attachments required.</p>
        </div>
        <div class="feature">
          <img src="./images/icon-any-file.svg" alt="Any File Type" class="feature-icon">
          <h3>Store any type of file</h3>
          <p>Whether you're sharing holidays photos or work documents, Fylo has you covered allowing for all file types to be securely stored and shared.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Stay Productive Section -->
  <section class="stay-productive">
    <div class="container">
      <div class="productive-content">
        <img src="./images/illustration-stay-productive.png" alt="Stay Productive" class="productive-illustration">
        <div class="productive-text">
          <h2>Stay productive, wherever you are</h2>
          <p>Never let location be an issue when accessing your files. Fylo has you covered for all of your file storage needs.</p>
          <p>Securely share files and folders with friends, family and colleagues for live collaboration. No email attachments required.</p>
          <a href="#" class="see-how-link">
            See how Fylo works
            <img src="./images/icon-arrow.svg" alt="Arrow" class="arrow-icon">
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="testimonials">
    <div class="container">
      <div class="testimonials-grid">
        <div class="testimonial">
          <p>Fylo has improved our team productivity by an order of magnitude. Since making the switch our team has become a well-oiled collaboration machine.</p>
          <div class="testimonial-author">
            <img src="./images/profile-1.jpg" alt="Satish Patel" class="author-avatar">
            <div class="author-info">
              <h4>Satish Patel</h4>
              <p>Founder & CEO, Huddle</p>
            </div>
          </div>
        </div>
        <div class="testimonial">
          <p>Fylo has improved our team productivity by an order of magnitude. Since making the switch our team has become a well-oiled collaboration machine.</p>
          <div class="testimonial-author">
            <img src="./images/profile-2.jpg" alt="Bruce McKenzie" class="author-avatar">
            <div class="author-info">
              <h4>Bruce McKenzie</h4>
              <p>Founder & CEO, Huddle</p>
            </div>
          </div>
        </div>
        <div class="testimonial">
          <p>Fylo has improved our team productivity by an order of magnitude. Since making the switch our team has become a well-oiled collaboration machine.</p>
          <div class="testimonial-author">
            <img src="./images/profile-3.jpg" alt="Iva Boyd" class="author-avatar">
            <div class="author-info">
              <h4>Iva Boyd</h4>
              <p>Founder & CEO, Huddle</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="cta">
    <div class="container">
      <div class="cta-box">
        <h2>Get early access today</h2>
        <p>It only takes a minute to sign up and our free starter tier is extremely generous. If you have any questions, our support team would be happy to help you.</p>
        <form class="cta-form">
          <input type="email" placeholder="<EMAIL>" class="cta-input" required>
          <button type="submit" class="cta-btn">Get Started For Free</button>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <img src="./images/logo.svg" alt="Fylo" class="footer-logo">
      <div class="footer-content">
        <div class="footer-contact">
          <div class="contact-item">
            <img src="./images/icon-location.svg" alt="Location" class="contact-icon">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>
          </div>
          <div class="contact-item">
            <img src="./images/icon-phone.svg" alt="Phone" class="contact-icon">
            <p>******-123-4567</p>
          </div>
          <div class="contact-item">
            <img src="./images/icon-email.svg" alt="Email" class="contact-icon">
            <p><EMAIL></p>
          </div>
        </div>
        <div>
          <ul class="footer-links">
            <li><a href="#">About Us</a></li>
            <li><a href="#">Jobs</a></li>
            <li><a href="#">Press</a></li>
            <li><a href="#">Blog</a></li>
          </ul>
        </div>
        <div>
          <ul class="footer-links">
            <li><a href="#">Contact Us</a></li>
            <li><a href="#">Terms</a></li>
            <li><a href="#">Privacy</a></li>
          </ul>
        </div>
        <div class="footer-social">
          <div class="social-links">
            <a href="#" class="social-link" aria-label="Facebook">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="social-link" aria-label="Twitter">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="social-link" aria-label="Instagram">
              <i class="fab fa-instagram"></i>
            </a>
          </div>
        </div>
      </div>
      <p class="attribution">
        Challenge by <a href="https://www.frontendmentor.io?ref=challenge" target="_blank">Frontend Mentor</a>.
        Coded by <a href="#">Your Name Here</a>.
      </p>
    </div>
  </footer>
</body>
</html>