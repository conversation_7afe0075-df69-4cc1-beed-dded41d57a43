# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Navy 850 (intro and email sign up background): hsl(217, 28%, 15%)
- Navy 900 (main background): hsl(218, 28%, 13%)
- Navy 950 (footer background): hsl(216, 53%, 9%)
- Navy 800 (testimonials background): hsl(219, 30%, 18%)

### Accent

- Teal 200 (inside call-to-action gradient): hsl(176, 68%, 64%)
- <PERSON><PERSON> 500 (inside call-to-action gradient): hsl(198, 60%, 50%)
- Red 500 (error): hsl(0, 100%, 63%)

### Neutral

- White: hsl(0, 0%, 100%)

## Typography

### Body Copy

- Font size: 14px

### Headings, Call-to-actions, Header Navigation

- Family: [Raleway](https://fonts.google.com/specimen/Raleway)
- Weights: 400, 700

### Body

- Family: [Open Sans](https://fonts.google.com/specimen/Open+Sans)
- Weights: 400, 700

## Icons

For the social icons, you can use a font icon library. Some suggestions can be found below:

- [Font Awesome](https://fontawesome.com/)
- [IcoMoon](https://icomoon.io/)
- [Ionicons](https://ionicons.com/)

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
